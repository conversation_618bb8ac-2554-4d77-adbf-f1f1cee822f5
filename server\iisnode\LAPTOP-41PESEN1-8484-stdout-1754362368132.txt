上传目录结构初始化完成
[2025-08-05 02:52:59] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 02:52:59] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 02:52:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:52:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:52:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:53:17] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:17] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:17] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:17] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:17] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:17] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:53:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:53:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:53:24] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:24] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:24] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:24] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:24] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:24] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:53:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:53:24] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:24] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:24] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 02:53:24] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 02:53:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:53:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:53:43] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:53:43] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:53:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:53:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:53:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:53:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:53:45] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:53:45] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:53:46] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:53:46] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:54:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:00] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":66}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 02:54:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 02:54:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
天地图代理请求: 纬度=24.531118435329862, 经度=118.1821609157986
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区金山街道泗水道617-101',
    location: { lon: 118.182161, lat: 24.531118 },
    addressComponent: {
      address: '泗水道617-101',
      town: '金山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '东北',
      county: '湖里区',
      city_code: '*********',
      address_position: '东北',
      poi: '美宜佳F389',
      province_code: '*********',
      town_code: '*********005',
      province: '福建省',
      road: '木浦路',
      road_distance: 119,
      address_distance: 41,
      poi_distance: 41
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-08-05 02:54:23] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:54:52] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6060,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 02:54:53] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 02:55:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:55:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
[2025-08-05 02:55:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
[2025-08-05 02:56:16] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:56:16] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:56:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 59
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 59
[2025-08-05 02:56:30] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 59
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 59
[2025-08-05 02:56:30] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
[2025-08-05 02:56:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:56:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:56:38] [DEBUG] 缓存设置成功 | {"key":"user_feedback:2023043:1007:50:0","ttl":180000}
[2025-08-05 02:56:38] [DEBUG] 缓存设置成功 | {"key":"user_feedback:2023043:1007:50:0","ttl":180000}
[2025-08-05 02:56:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:57:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:57:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:57:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:57:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:57:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:57:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:57:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 02:57:47] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:57:47] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:57:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 02:57:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:57:49] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:57:49] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:57:50] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:57:50] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:57:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 02:57:52] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 02:57:52] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 02:59:19] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 59
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 59
[2025-08-05 02:59:23] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
[2025-08-05 02:59:23] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:01:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:01:05] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:01:05] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:01:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:01:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 03:01:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:01:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:01:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:01:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:01:09] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:01:09] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:01:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 03:01:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:01:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:02:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:02:14] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:02:14] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:02:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:02:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:02:31] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":67}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 03:02:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 03:02:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:02:39] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:02:39] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:02:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 59
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 59
[2025-08-05 03:02:40] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
[2025-08-05 03:02:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:04:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:04:31] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:04:31] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:04:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:04:59] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 03:04:59] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 03:04:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:04:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 03:04:59] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 03:04:59] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 03:04:59] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 03:04:59] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 03:04:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 03:04:59] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 03:04:59] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 03:05:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:05:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:05:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:05:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 03:05:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 03:05:10] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:05:10] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:05:12] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:05:12] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:05:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 03:05:14] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 03:05:14] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 03:05:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:05:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:05:17] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":67}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 03:05:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 03:05:19] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:05:19] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 03:05:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
天地图代理请求: 纬度=24.5118, 经度=118.14577
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城',
    location: { lon: 118.14577, lat: 24.5118 },
    addressComponent: {
      address: '金湖三里30-46禹洲·香槟城',
      town: '禾山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '西北',
      county: '湖里区',
      city_code: '*********',
      address_position: '西北',
      poi: '禹洲·香槟城北门',
      province_code: '*********',
      town_code: '*********003',
      province: '福建省',
      road: '金湖路',
      road_distance: 67,
      address_distance: 31,
      poi_distance: 31
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-08-05 03:05:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 03:06:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 03:06:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 03:06:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 03:06:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
